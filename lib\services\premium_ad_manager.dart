import 'dart:async';
import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'package:google_mobile_ads/google_mobile_ads.dart';
import 'package:get/get.dart';

/// Centralized ad management service for optimal performance
class PremiumAdManager extends GetxController {
  static PremiumAdManager get to => Get.find();

  // Ad cache with permanent storage
  final Map<String, BannerAd> _adCache = {};
  final Map<String, AdLoadState> _adStates = {};
  final Map<String, DateTime> _lastLoadAttempt = {};
  final Map<String, int> _retryCount = {};

  // Configuration
  static const int maxConcurrentAds = 12; // Increased for better performance
  static const int maxRetries = 3;
  static const Duration retryDelay = Duration(seconds: 5);
  static const Duration adLifetime = Duration(minutes: 30); // Reduced for more frequent cleanup
  
  // Track initialization
  bool _isInitialized = false;
  final _initCompleter = Completer<void>();
  
  // Test ad unit IDs for development
  final Map<String, String> _testAdUnits = {
    'android': 'ca-app-pub-3940256099942544/6300978111',
    'ios': 'ca-app-pub-3940256099942544/2934735716',
  };

  // Production ad unit IDs
  final Map<String, String> _androidAdUnits = {
    'home_banner_1': 'ca-app-pub-8639821055582439/1096152024',
    'home_banner_2': 'ca-app-pub-8639821055582439/9477204925',
    'home_banner_3': 'ca-app-pub-8639821055582439/4156563660',
    'home_banner_default': 'ca-app-pub-8639821055582439/8056296139',
  };

  final Map<String, String> _iosAdUnits = {
    'home_banner_1': 'ca-app-pub-8639821055582439/3769550858',
    'home_banner_2': 'ca-app-pub-8639821055582439/4265224933',
    'home_banner_3': 'ca-app-pub-8639821055582439/1746888714',
    'home_banner_default': 'ca-app-pub-8639821055582439/2021354915',
  };

  @override
  void onInit() {
    super.onInit();
    _initializeAds();
  }
  
  /// Initialize Mobile Ads SDK
  Future<void> _initializeAds() async {
    try {
      debugPrint('🚀 Initializing Mobile Ads SDK...');
      await MobileAds.instance.initialize();
      
      // Update request configuration for better fill rate
      await MobileAds.instance.updateRequestConfiguration(
        RequestConfiguration(
          testDeviceIds: kDebugMode ? ['YOUR_TEST_DEVICE_ID'] : [],
          maxAdContentRating: MaxAdContentRating.g,
          tagForChildDirectedTreatment: TagForChildDirectedTreatment.unspecified,
        ),
      );
      
      _isInitialized = true;
      _initCompleter.complete();
      debugPrint('✅ Mobile Ads SDK initialized successfully');
      
      // Start periodic cleanup
      _startPeriodicCleanup();
    } catch (e) {
      debugPrint('❌ Failed to initialize Mobile Ads SDK: $e');
      _isInitialized = false;
      if (!_initCompleter.isCompleted) {
        _initCompleter.completeError(e);
      }
    }
  }
  
  /// Wait for initialization to complete
  Future<void> ensureInitialized() async {
    if (_isInitialized) return;
    try {
      await _initCompleter.future.timeout(
        const Duration(seconds: 10),
        onTimeout: () {
          throw TimeoutException('Ad initialization timeout');
        },
      );
    } catch (e) {
      debugPrint('⚠️ Ad initialization failed or timed out: $e');
    }
  }

  /// Get ad unit ID based on position and platform
  String getAdUnitId(String key) {
    // Use test ads in debug mode
    if (kDebugMode) {
      return Platform.isAndroid ? _testAdUnits['android']! : _testAdUnits['ios']!;
    }
    
    final adUnits = Platform.isAndroid ? _androidAdUnits : _iosAdUnits;
    return adUnits[key] ?? adUnits['home_banner_default']!;
  }
  
  /// Get appropriate ad size based on width and height
  AdSize _getOptimalAdSize(double width, double height) {
    final screenWidth = width.toInt();
    final screenHeight = height.toInt();
    
    // For square/rectangle containers, use medium rectangle
    if (screenHeight >= 200) {
      // Medium Rectangle - standard size that fills space nicely
      return AdSize.mediumRectangle; // 300x250
    } else {
      // For smaller heights, use appropriate banner
      return AdSize.largeBanner; // 320x100
    }
  }

  /// Get or create ad with intelligent caching
  Future<BannerAd?> getAd(
    String key, {
    required Size size,
    String? customAdUnitId,
  }) async {
    // Ensure ads are initialized
    await ensureInitialized();
    
    // Check if ad is already loaded
    final cachedAd = _adCache[key];
    if (cachedAd != null && _isAdValid(key)) {
      debugPrint('✅ Using cached ad for key: $key');
      return cachedAd;
    }

    // Check if ad is currently loading
    if (_adStates[key] == AdLoadState.loading) {
      debugPrint('⏳ Ad already loading for key: $key');
      // Wait for current load to complete
      await _waitForAdLoad(key);
      return _adCache[key];
    }

    // Check retry count
    final retries = _retryCount[key] ?? 0;
    if (retries >= maxRetries) {
      final lastAttempt = _lastLoadAttempt[key];
      if (lastAttempt != null &&
          DateTime.now().difference(lastAttempt) < const Duration(minutes: 5)) {
        debugPrint('❌ Max retries reached for key: $key');
        return null;
      } else {
        // Reset retry count after cooldown
        _retryCount[key] = 0;
      }
    }

    // Check concurrent ad limit
    if (_getActiveAdCount() >= maxConcurrentAds) {
      debugPrint('⚠️ Max concurrent ads reached, cleaning up old ads');
      _cleanupOldAds();
    }

    // Load new ad
    return _loadAd(key, size, customAdUnitId);
  }

  /// Load a new banner ad with retry logic
  Future<BannerAd?> _loadAd(
      String key, Size size, String? customAdUnitId) async {
    _adStates[key] = AdLoadState.loading;
    _lastLoadAttempt[key] = DateTime.now();

    try {
      final adUnitId = customAdUnitId ?? getAdUnitId(key);
      final adSize = _getOptimalAdSize(size.width, size.height);
      
      debugPrint('📱 Loading ad for key: $key with size: ${adSize.width}x${adSize.height}');

      final ad = BannerAd(
        adUnitId: adUnitId,
        size: adSize,
        request: const AdRequest(),
        listener: BannerAdListener(
          onAdLoaded: (ad) {
            debugPrint('✅ Ad loaded successfully for key: $key');
            _adCache[key] = ad as BannerAd;
            _adStates[key] = AdLoadState.loaded;
            _retryCount[key] = 0; // Reset retry count on success
          },
          onAdFailedToLoad: (ad, error) {
            debugPrint('❌ Ad failed to load for key: $key - ${error.message}');
            debugPrint('   Error code: ${error.code}');
            debugPrint('   Error domain: ${error.domain}');
            ad.dispose();
            _adStates[key] = AdLoadState.failed;
            
            // Increment retry count
            _retryCount[key] = (_retryCount[key] ?? 0) + 1;
            
            // Schedule retry if under limit
            if ((_retryCount[key] ?? 0) < maxRetries) {
              Timer(retryDelay * (_retryCount[key] ?? 1), () {
                debugPrint('🔄 Retrying ad load for key: $key (attempt ${_retryCount[key]})');
                _loadAd(key, size, customAdUnitId);
              });
            }
          },
          onAdOpened: (ad) {
            debugPrint('📱 Ad opened for key: $key');
          },
          onAdClosed: (ad) {
            debugPrint('📱 Ad closed for key: $key');
          },
          onAdImpression: (ad) {
            debugPrint('👁️ Ad impression for key: $key');
          },
          onAdClicked: (ad) {
            debugPrint('👆 Ad clicked for key: $key');
          },
        ),
      );

      await ad.load();

      // Wait for ad to be loaded or failed
      await _waitForAdLoad(key, timeout: const Duration(seconds: 15));

      return _adCache[key];
    } catch (e) {
      debugPrint('❌ Exception loading ad for key: $key - $e');
      _adStates[key] = AdLoadState.failed;
      
      // Schedule retry
      _retryCount[key] = (_retryCount[key] ?? 0) + 1;
      if ((_retryCount[key] ?? 0) < maxRetries) {
        Timer(retryDelay * (_retryCount[key] ?? 1), () {
          _loadAd(key, size, customAdUnitId);
        });
      }
      
      return null;
    }
  }

  /// Wait for ad to finish loading
  Future<void> _waitForAdLoad(String key, {Duration? timeout}) async {
    final completer = Completer<void>();
    Timer? timeoutTimer;

    if (timeout != null) {
      timeoutTimer = Timer(timeout, () {
        if (!completer.isCompleted) {
          completer.complete();
        }
      });
    }

    // Poll for ad state change
    Timer.periodic(const Duration(milliseconds: 100), (timer) {
      if (_adStates[key] != AdLoadState.loading || completer.isCompleted) {
        timer.cancel();
        timeoutTimer?.cancel();
        if (!completer.isCompleted) {
          completer.complete();
        }
      }
    });

    await completer.future;
  }

  /// Check if cached ad is still valid
  bool _isAdValid(String key) {
    final lastAttempt = _lastLoadAttempt[key];
    if (lastAttempt == null) return false;

    // Consider ad invalid after lifetime expires
    return DateTime.now().difference(lastAttempt) < adLifetime;
  }

  /// Get count of active ads
  int _getActiveAdCount() {
    return _adCache.length;
  }

  /// Clean up old ads to free memory
  void _cleanupOldAds() {
    final now = DateTime.now();
    final keysToRemove = <String>[];

    _lastLoadAttempt.forEach((key, loadTime) {
      if (now.difference(loadTime) > adLifetime) {
        keysToRemove.add(key);
      }
    });

    for (final key in keysToRemove) {
      disposeAd(key);
    }

    if (keysToRemove.isNotEmpty) {
      debugPrint('🧹 Cleaned up ${keysToRemove.length} old ads');
    }
  }

  /// Clean up ads that are far from current scroll position
  void cleanupDistantAds(String currentKeyPrefix, int currentIndex) {
    final keysToRemove = <String>[];
    
    _adCache.forEach((key, ad) {
      if (key.startsWith(currentKeyPrefix)) {
        // Extract index from key (e.g., "list_ad_5" -> 5)
        final match = RegExp(r'\d+$').firstMatch(key);
        if (match != null) {
          final adIndex = int.parse(match.group(0)!);
          // Remove ads that are more than 10 positions away
          if ((adIndex - currentIndex).abs() > 10) {
            keysToRemove.add(key);
          }
        }
      }
    });
    
    for (final key in keysToRemove) {
      disposeAd(key);
    }
    
    if (keysToRemove.isNotEmpty) {
      debugPrint('🧹 Cleaned up ${keysToRemove.length} distant ads');
    }
  }

  /// Dispose specific ad
  void disposeAd(String key) {
    _adCache[key]?.dispose();
    _adCache.remove(key);
    _adStates.remove(key);
    _lastLoadAttempt.remove(key);
    _retryCount.remove(key);
  }

  /// Dispose all ads
  void disposeAllAds() {
    for (final ad in _adCache.values) {
      ad.dispose();
    }
    _adCache.clear();
    _adStates.clear();
    _lastLoadAttempt.clear();
    _retryCount.clear();
    debugPrint('🧹 Disposed all ads');
  }

  /// Start periodic cleanup task
  void _startPeriodicCleanup() {
    Timer.periodic(const Duration(minutes: 5), (_) {
      _cleanupOldAds();
    });
  }

  /// Preload ads for better performance with improved strategy
  Future<void> preloadAds(List<String> keys, Size size) async {
    debugPrint('🚀 Preloading ${keys.length} ads with size: ${size.width}x${size.height}');
    
    // Clean up old ads first to make room
    _cleanupOldAds();
    
    // Ensure initialization before preloading
    await ensureInitialized();

    // Load ads with staggered approach for better performance
    for (int i = 0; i < keys.length; i++) {
      final key = keys[i];
      
      // Skip if already loaded or loading
      if (_adCache.containsKey(key) || _adStates[key] == AdLoadState.loading) {
        continue;
      }
      
      // Start loading without waiting
      getAd(key, size: size).then((_) {
        debugPrint('✅ Preloaded ad for key: $key');
      }).catchError((e) {
        debugPrint('⚠️ Failed to preload ad for key: $key - $e');
      });
      
      // Small delay between starts to avoid overwhelming the system
      if (i < keys.length - 1) {
        await Future.delayed(const Duration(milliseconds: 100));
      }
    }
    
    // Log cache statistics after a delay
    Future.delayed(const Duration(seconds: 2), () {
      final stats = getCacheStats();
      debugPrint('📊 Ad preload status - Loaded: ${stats['loadedAds']}, Failed: ${stats['failedAds']}, Loading: ${stats['loadingAds']}');
    });
  }

  /// Get ad state for debugging
  AdLoadState? getAdState(String key) => _adStates[key];

  /// Get cache statistics
  Map<String, dynamic> getCacheStats() {
    return {
      'initialized': _isInitialized,
      'totalAds': _adCache.length,
      'loadedAds':
          _adStates.values.where((s) => s == AdLoadState.loaded).length,
      'failedAds':
          _adStates.values.where((s) => s == AdLoadState.failed).length,
      'loadingAds':
          _adStates.values.where((s) => s == AdLoadState.loading).length,
    };
  }

  @override
  void onClose() {
    disposeAllAds();
    super.onClose();
  }
}

/// Ad loading states
enum AdLoadState {
  idle,
  loading,
  loaded,
  failed,
}

/// Premium ad widget with automatic state management
class PremiumAdWidget extends StatefulWidget {
  final String adKey;
  final double width;
  final double height;
  final Widget? placeholder;
  final Widget? errorWidget;

  const PremiumAdWidget({
    Key? key,
    required this.adKey,
    required this.width,
    required this.height,
    this.placeholder,
    this.errorWidget,
  }) : super(key: key);

  @override
  State<PremiumAdWidget> createState() => _PremiumAdWidgetState();
}

class _PremiumAdWidgetState extends State<PremiumAdWidget> 
    with AutomaticKeepAliveClientMixin {
  BannerAd? _ad;
  AdLoadState _state = AdLoadState.idle;
  bool _isDisposed = false;

  @override
  bool get wantKeepAlive => true;

  @override
  void initState() {
    super.initState();
    // Delay initial load to ensure proper initialization
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (!_isDisposed) {
        _loadAd();
      }
    });
  }

  Future<void> _loadAd() async {
    if (!mounted || _isDisposed) return;

    setState(() {
      _state = AdLoadState.loading;
    });

    try {
      final ad = await PremiumAdManager.to.getAd(
        widget.adKey,
        size: Size(widget.width, widget.height),
      );

      if (mounted && !_isDisposed) {
        setState(() {
          _ad = ad;
          _state = ad != null ? AdLoadState.loaded : AdLoadState.failed;
        });
      }
    } catch (e) {
      debugPrint('⚠️ Error in PremiumAdWidget: $e');
      if (mounted && !_isDisposed) {
        setState(() {
          _state = AdLoadState.failed;
        });
      }
    }
  }
  
  @override
  void dispose() {
    _isDisposed = true;
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    
    return AnimatedContainer(
      duration: const Duration(milliseconds: 300),
      width: widget.width,
      height: widget.height,
      decoration: BoxDecoration(
        color: _state == AdLoadState.loaded 
            ? Colors.transparent 
            : Colors.grey[50],
        borderRadius: BorderRadius.circular(12),
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(12),
        child: _buildContent(),
      ),
    );
  }

  Widget _buildContent() {
    switch (_state) {
      case AdLoadState.idle:
      case AdLoadState.loading:
        return widget.placeholder ?? _buildPremiumPlaceholder();

      case AdLoadState.loaded:
        if (_ad != null) {
          return _buildAdWithAnimation();
        }
        return widget.errorWidget ?? _buildPremiumError();

      case AdLoadState.failed:
        return widget.errorWidget ?? _buildPremiumError();
    }
  }
  
  Widget _buildAdWithAnimation() {
    return AnimatedOpacity(
      opacity: 1.0,
      duration: const Duration(milliseconds: 500),
      child: AdWidget(ad: _ad!),
    );
  }

  Widget _buildPremiumPlaceholder() {
    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            Colors.grey[100]!,
            Colors.grey[200]!,
          ],
        ),
      ),
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            SizedBox(
              width: 32,
              height: 32,
              child: CircularProgressIndicator(
                strokeWidth: 3.0,
                valueColor: AlwaysStoppedAnimation<Color>(
                  Colors.grey[400]!,
                ),
              ),
            ),
            const SizedBox(height: 12),
            Text(
              'Načítání reklamy...',
              style: TextStyle(
                color: Colors.grey[600],
                fontSize: 13,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPremiumError() {
    return GestureDetector(
      onTap: () {
        // Retry loading on tap
        if (mounted && !_isDisposed) {
          _loadAd();
        }
      },
      child: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              Colors.grey[50]!,
              Colors.grey[100]!,
            ],
          ),
        ),
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.refresh_rounded,
                size: 36,
                color: Colors.grey[400],
              ),
              const SizedBox(height: 12),
              Text(
                'Klepněte pro načtení',
                style: TextStyle(
                  color: Colors.grey[600],
                  fontSize: 13,
                  fontWeight: FontWeight.w500,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                'Obsah se načítá',
                style: TextStyle(
                  color: Colors.grey[500],
                  fontSize: 11,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
